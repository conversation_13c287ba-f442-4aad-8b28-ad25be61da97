### 蓝色渐变背景风格，蓝色渐变样式

- 主要渐变色：linear-gradient(180deg, #124B9A, #0D438D)
- 按钮渐变：linear-gradient(0deg, #0096FF, #043475)
- 输入框渐变：linear-gradient(0deg, #0D3A8D, #03256B)
- 表头渐变：linear-gradient(90deg, #0d438d 0%, rgba(#0d438d,0.4) 100%)

#### 整体样式
1. 页面整体背景
   添加了与系统一致的蓝色渐变背景：linear-gradient(180deg, #124B9A, #0D438D)
   增加了动态背景效果，包含径向渐变和浮动动画
   设置了最小高度为 100vh，确保全屏覆盖
2. 文字颜色调整
   标题使用白色文字配合渐变效果：linear-gradient(135deg, #17d2ef 0%, #ffffff 100%)
   描述文字使用半透明白色：rgba(255, 255, 255, 0.9)
   添加了文字阴影效果，增强可读性
3. 控制面板和卡片样式
   使用毛玻璃效果：backdrop-filter: blur(10px)
   半透明背景：rgba(255, 255, 255, 0.1)
   添加了边框和阴影效果，增强层次感
4. 表单元素样式
   下拉选择框使用系统标准的蓝色渐变：linear-gradient(0deg, #0D3A8D, #03256B)
   输入框采用相同的渐变背景和边框样式
   文本区域使用一致的样式风格
5. 按钮样式
   主要按钮使用系统标准渐变：linear-gradient(0deg, #0096FF, #043475)
   添加了悬停效果和阴影
   不同类型按钮使用对应的渐变色彩
6. 对话框样式
   对话框背景使用半透明蓝色渐变
   增强了毛玻璃效果
   代码块使用深色半透明背景，提高代码可读性
7. 选项和结果区域
   成功/错误状态使用半透明彩色背景
   保持了良好的对比度和可读性
   统一了整体视觉风格

#### 编辑器样式
1. 滚动条样式优化
   宽度调整：将滚动条宽度从默认的较宽尺寸调整为 8px，更加精致
   Firefox兼容：使用 scrollbar-width: thin 和 scrollbar-color 属性
   Webkit浏览器：自定义 ::-webkit-scrollbar 系列伪元素
2. 滚动条视觉效果
   轨道样式：半透明白色背景 rgba(255, 255, 255, 0.1)，圆角设计
   滑块样式：蓝色渐变 linear-gradient(0deg, #70a7cb, #5a8db3)
   悬停效果：滑块悬停时颜色加深，提供视觉反馈
   边框装饰：添加半透明白色边框，增强立体感
3. 调整大小手柄优化
   自定义样式：使用 ::-webkit-resizer 伪元素
   渐变背景：135度角的蓝色渐变，与滚动条保持一致
   圆角设计：右下角圆角 4px 0 4px 0
   立体效果：添加边框和阴影，增强可操作性的视觉提示
4. 一致性设计
   JSON编辑器和导出文本区域使用相同的滚动条和调整手柄样式
   与系统整体的蓝色渐变主题保持一致
   所有交互元素都有适当的悬停效果
