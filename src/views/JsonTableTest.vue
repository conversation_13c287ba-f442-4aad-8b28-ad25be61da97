<template>
  <div class="json-table-test">
    <h1>JsonTableContainer 测试页面</h1>
    <p>用于测试简化后的嵌套表格功能和右键菜单修复</p>

    <!-- 测试按钮 -->
    <div class="test-controls">
      <button @click="loadTestData" class="test-btn">加载测试数据</button>
      <button @click="addNestedTable" class="test-btn">添加嵌套表格</button>
      <button @click="exportData" class="test-btn">导出数据</button>
      <button @click="clearData" class="test-btn">清空数据</button>
    </div>

    <!-- 操作提示 -->
    <div class="test-tips">
      <h3>测试说明：</h3>
      <ul>
        <li>右键点击单元格测试右键菜单位置是否正确</li>
        <li>测试列宽调整功能是否正常工作</li>
        <li>测试嵌套表格的创建和编辑功能</li>
        <li>嵌套表格已简化，无表头，直接显示可编辑内容</li>
      </ul>
    </div>

    <!-- 表格组件 -->
    <div class="table-container">
      <JsonTableContainer
        ref="jsonTable"
        :table-width="'100%'"
        :table-height="'500px'"
        :data-rows="tableData"
        :enable-nested-tables="true"
        :max-nested-level="2"
        @nested-table-created="onNestedTableCreated"
        @nested-table-removed="onNestedTableRemoved"
        @nested-cell-start-edit="onNestedCellStartEdit"
        @nested-cell-finish-edit="onNestedCellFinishEdit"
        @column-width-changed="onColumnWidthChanged"
        @table-updated="onTableUpdated"
      />
    </div>

    <!-- 事件日志 -->
    <div class="event-log">
      <h3>事件日志：</h3>
      <div class="log-content">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
      <button @click="clearLogs" class="clear-logs-btn">清空日志</button>
    </div>
  </div>
</template>

<script>
import JsonTableContainer from '@/components/JsonTableContainer.vue'

export default {
  name: 'JsonTableTest',
  components: {
    JsonTableContainer
  },
  data() {
    return {
      tableData: [
        Array(4).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ],
      eventLogs: []
    }
  },
  methods: {
    // 加载测试数据
    loadTestData() {
      const testData = {
        "headerConfig": {
          "headers": [["项目", "详细信息", "状态", "备注"]],
          "merges": []
        },
        "headerWidthConfig": {
          "columnWidths": [150, 300, 100, 150],
          "headerHeights": [40],
          "verticalHeaders": [false, false, false, false]
        },
        "cellRows": [
          [
            {"content": "测试项目1", "originContent": "测试项目1"},
            {
              "content": "包含嵌套表格的详细信息",
              "originContent": "包含嵌套表格的详细信息",
              "nestedTable": {
                "enabled": true,
                "config": {
                  "columnWidths": [100, 80, 80],
                  "cellRows": [
                    [
                      {"content": "子项目A", "originContent": "子项目A"},
                      {"content": "进行中", "originContent": "进行中"},
                      {"content": "张三", "originContent": "张三"}
                    ],
                    [
                      {"content": "子项目B", "originContent": "子项目B"},
                      {"content": "已完成", "originContent": "已完成"},
                      {"content": "李四", "originContent": "李四"}
                    ]
                  ],
                  "metadata": {
                    "title": "子项目详情",
                    "columns": 3,
                    "rows": 2
                  }
                }
              }
            },
            {"content": "进行中", "originContent": "进行中"},
            {"content": "重要", "originContent": "重要"}
          ],
          [
            {"content": "测试项目2", "originContent": "测试项目2"},
            {"content": "普通信息", "originContent": "普通信息"},
            {"content": "已完成", "originContent": "已完成"},
            {"content": "一般", "originContent": "一般"}
          ]
        ]
      }

      this.$refs.jsonTable.insertDataFromJSON(testData, {
        clearExisting: true
      })

      this.addLog('数据加载', '测试数据已加载')
    },

    // 添加嵌套表格（通过代码）
    addNestedTable() {
      // 确保有数据行
      if (this.tableData.length === 0) {
        this.loadTestData()
        return
      }

      // 在第一行第二列添加嵌套表格
      const cell = this.tableData[0][1]
      if (cell && !this.$refs.jsonTable.hasNestedTable(cell)) {
        const nestedTableConfig = {
          enabled: true,
          config: {
            columnWidths: [120, 80],
            cellRows: [
              [
                {"content": "动态添加1", "originContent": "动态添加1"},
                {"content": "测试", "originContent": "测试"}
              ],
              [
                {"content": "动态添加2", "originContent": "动态添加2"},
                {"content": "成功", "originContent": "成功"}
              ]
            ],
            metadata: {
              title: "动态嵌套表格",
              columns: 2,
              rows: 2
            }
          }
        }

        this.$set(cell, 'nestedTable', nestedTableConfig)
        this.addLog('嵌套表格创建', '通过代码动态添加嵌套表格')
      }
    },

    // 导出数据
    exportData() {
      const data = this.$refs.jsonTable.getDataAsJSON({
        includeNestedTables: true
      })
      console.log('导出的数据:', data)
      this.addLog('数据导出', '数据已导出到控制台')
    },

    // 清空数据
    clearData() {
      this.$refs.jsonTable.clearAllData()
      this.addLog('数据清空', '所有数据已清空')
    },

    // 事件处理方法
    onNestedTableCreated(event) {
      this.addLog('嵌套表格创建', `位置: (${event.rowIndex}, ${event.cellIndex})`)
    },

    onNestedTableRemoved(event) {
      this.addLog('嵌套表格移除', `位置: (${event.rowIndex}, ${event.cellIndex})`)
    },

    onNestedCellStartEdit(event) {
      this.addLog('嵌套单元格编辑开始', `位置: (${event.parentRowIndex}, ${event.parentCellIndex}) -> (${event.nestedRowIndex}, ${event.nestedCellIndex})`)
    },

    onNestedCellFinishEdit(event) {
      this.addLog('嵌套单元格编辑完成', `内容: ${event.newContent}`)
    },

    onColumnWidthChanged(event) {
      this.addLog('列宽调整', `列${event.columnIndex}: ${event.newWidth}px`)
    },

    onTableUpdated(event) {
      this.addLog('表格更新', '表格已更新')
    },

    // 日志管理
    addLog(event, data) {
      const now = new Date()
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
      
      this.eventLogs.unshift({
        time,
        event,
        data
      })

      // 限制日志数量
      if (this.eventLogs.length > 50) {
        this.eventLogs = this.eventLogs.slice(0, 50)
      }
    },

    clearLogs() {
      this.eventLogs = []
    }
  },
  mounted() {
    this.addLog('页面加载', 'JsonTableContainer 测试页面已加载')
  }
}
</script>

<style scoped>
.json-table-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 10px;
}

p {
  color: #666;
  margin-bottom: 20px;
}

.test-controls {
  margin-bottom: 20px;
}

.test-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  margin-right: 10px;
  border-radius: 4px;
  cursor: pointer;
}

.test-btn:hover {
  background-color: #0056b3;
}

.test-tips {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.test-tips h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.test-tips ul {
  margin: 0;
  padding-left: 20px;
}

.test-tips li {
  margin-bottom: 5px;
  color: #555;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.event-log {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
}

.event-log h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.log-item {
  display: flex;
  padding: 4px 0;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  width: 80px;
  flex-shrink: 0;
}

.log-event {
  color: #007bff;
  width: 120px;
  flex-shrink: 0;
  font-weight: bold;
}

.log-data {
  color: #333;
  flex: 1;
}

.clear-logs-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-logs-btn:hover {
  background-color: #5a6268;
}
</style>
