package com.logictrue.word.controller;

import com.logictrue.common.core.web.controller.BaseController;
import com.logictrue.word.dto.CheckRecordExportRequest;
import com.logictrue.word.service.CheckRecordExportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 检验记录导出控制器
 */
@Slf4j
@RestController
@RequestMapping("/checkRecordExport")
@RequiredArgsConstructor
@Api(value = "检验记录导出", tags = "检验记录导出")
public class CheckRecordExportController extends BaseController {

    private final CheckRecordExportService checkRecordExportService;

    /**
     * 导出检验记录当前页
     */
    @PostMapping("/exportCurrentPage")
    @ApiOperation(value = "导出检验记录当前页")
    public ResponseEntity<byte[]> exportCurrentPage(@RequestBody CheckRecordExportRequest request) {
        try {
            log.info("接收到导出当前页请求，车辆ID: {}, 页面顺序: {}", request.getCarId(), request.getPageOrder());

            // 验证参数
            if (request.getCarId() == null || request.getCarId().trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body("车辆ID不能为空".getBytes(StandardCharsets.UTF_8));
            }

            if (request.getPageOrder() == null) {
                return ResponseEntity.badRequest()
                        .body("页面顺序不能为空".getBytes(StandardCharsets.UTF_8));
            }

            // 设置导出类型
            request.setExportType("current_page");

            // 导出Word文档
            byte[] wordBytes = checkRecordExportService.exportCheckRecord(request);

            // 生成文件名
            String fileName = generateFileName(request.getTitle(), "第" + request.getPageOrder() + "页");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("检验记录当前页导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);

            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);

        } catch (IOException e) {
            log.error("导出检验记录当前页失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("处理导出请求时发生未知错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("系统错误: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 导出检验记录全部页面
     */
    @PostMapping("/exportAllPages")
    @ApiOperation(value = "导出检验记录全部页面")
    public ResponseEntity<byte[]> exportAllPages(@RequestBody CheckRecordExportRequest request) {
        try {
            log.info("接收到导出全部页面请求，车辆ID: {}", request.getCarId());

            // 验证参数
            if (request.getCarId() == null || request.getCarId().trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body("车辆ID不能为空".getBytes(StandardCharsets.UTF_8));
            }

            // 设置导出类型
            request.setExportType("all_pages");

            // 导出Word文档
            byte[] wordBytes = checkRecordExportService.exportCheckRecord(request);

            // 生成文件名
            String fileName = generateFileName(request.getTitle(), "全部页面");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("检验记录全部页面导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);

            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);

        } catch (IOException e) {
            log.error("导出检验记录全部页面失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("处理导出请求时发生未知错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("系统错误: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 批量导出检验记录
     */
    @PostMapping("/exportBatch")
    @ApiOperation(value = "批量导出检验记录")
    public ResponseEntity<byte[]> exportBatch(@RequestBody CheckRecordExportRequest request) {
        try {
            log.info("接收到批量导出请求，车辆ID列表: {}", request.getCarIds());

            // 验证参数
            if (request.getCarIds() == null || request.getCarIds().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body("车辆ID列表不能为空".getBytes(StandardCharsets.UTF_8));
            }

            // 设置导出类型
            request.setExportType("batch");

            // 导出Word文档
            byte[] wordBytes = checkRecordExportService.exportCheckRecord(request);

            // 生成文件名
            String fileName = generateFileName(request.getTitle(), "批量导出");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("检验记录批量导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);

            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);

        } catch (IOException e) {
            log.error("批量导出检验记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("处理批量导出请求时发生未知错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("系统错误: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String title, String suffix) {
        try {
            String baseFileName = (title != null && !title.trim().isEmpty())
                    ? title.trim()
                    : "检验记录表";

            // 添加后缀
            if (suffix != null && !suffix.trim().isEmpty()) {
                baseFileName += "_" + suffix.trim();
            }

            // 添加时间戳
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = baseFileName + "_" + timestamp + ".docx";

            // URL编码文件名以支持中文
            return URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20");

        } catch (Exception e) {
            log.warn("生成文件名失败，使用默认文件名", e);
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            return "check_record_export_" + timestamp + ".docx";
        }
    }
}
