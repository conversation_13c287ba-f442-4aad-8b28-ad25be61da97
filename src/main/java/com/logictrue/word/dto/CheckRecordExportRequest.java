package com.logictrue.word.dto;

import lombok.Data;
import java.util.List;

/**
 * 检验记录导出请求DTO
 */
@Data
public class CheckRecordExportRequest {

    /**
     * 车辆ID
     */
    private String carId;

    /**
     * 导出类型：current_page(当前页), all_pages(全部页面), batch(批量导出)
     */
    private String exportType;

    /**
     * 页面顺序（当导出类型为current_page时使用）
     */
    private Integer pageOrder;

    /**
     * 车辆ID列表（当导出类型为batch时使用）
     */
    private List<String> carIds;

    /**
     * 是否包含检验记录数据
     */
    private Boolean includeCheckRecords = true;

    /**
     * 文档标题
     */
    private String title;

    /**
     * 页面方向 (PORTRAIT: 纵向, LANDSCAPE: 横向)
     */
    private String pageOrientation = "LANDSCAPE";

    /**
     * 导出配置
     */
    private ExportConfig exportConfig;

    /**
     * 导出配置
     */
    @Data
    public static class ExportConfig {
        /**
         * 是否包含空行
         */
        private Boolean includeEmptyRows = false;

        /**
         * 是否合并相同工序
         */
        private Boolean mergeSameProcess = true;

        /**
         * 每页最大行数（用于分页）
         */
        private Integer maxRowsPerPage = 50;

        /**
         * 是否添加页码
         */
        private Boolean addPageNumbers = true;

        /**
         * 是否添加导出时间
         */
        private Boolean addExportTime = true;
    }
}
