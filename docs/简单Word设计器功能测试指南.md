# 简单Word设计器功能测试指南

## 概述
本文档提供了简单Word设计器的功能测试指南，包括前端页面功能和后端导出功能的测试步骤。

## 功能列表

### 1. 文本编辑功能
- [x] 字体大小设置
- [x] 字体颜色设置
- [x] 字体加粗
- [x] 字体斜体
- [x] 字体下划线
- [x] 字体删除线
- [x] 字体对齐方式设置（左对齐、居中、右对齐）
- [x] 字体缩进设置
- [x] 字体间距设置
- [x] 字体行间距设置
- [x] 字体段落间距设置
- [x] 内容居中

### 2. 图片功能
- [x] 支持上传图片插入
- [x] 可以拖动改变图片位置
- [x] 图片大小调整功能

### 3. 页面设置功能
- [x] 纸张大小设置（A4、A3、Letter）
- [x] 页面方向设置（纵向、横向）
- [x] 页边距设置（上、下、左、右）
- [x] 页眉设置
- [x] 页脚设置
- [x] 页码设置
- [x] 页眉页脚字体样式设置

### 4. 导出功能
- [x] 导出为docx文件
- [x] 后端下载接口

## 测试步骤

### 前端功能测试

#### 1. 访问页面
1. 启动前端项目：`cd logictrue-ui-word && npm run dev`
2. 在浏览器中访问：`http://localhost:8080/simple-word-designer`
3. 验证页面是否正常加载，工具栏是否显示完整

#### 2. 文本编辑测试
1. **基本文本输入**
   - 在编辑区域输入文本
   - 验证文本是否正常显示

2. **字体设置测试**
   - 选择文本，更改字体（宋体、黑体、微软雅黑等）
   - 选择文本，更改字体大小（12px、14px、16px等）
   - 选择文本，更改字体颜色
   - 验证更改是否生效

3. **格式化测试**
   - 选择文本，点击加粗按钮，验证文本是否加粗
   - 选择文本，点击斜体按钮，验证文本是否倾斜
   - 选择文本，点击下划线按钮，验证文本是否有下划线
   - 选择文本，点击删除线按钮，验证文本是否有删除线

4. **对齐方式测试**
   - 选择段落，点击左对齐按钮
   - 选择段落，点击居中对齐按钮
   - 选择段落，点击右对齐按钮
   - 验证对齐效果是否正确

5. **缩进和间距测试**
   - 选择段落，点击增加缩进按钮
   - 选择段落，点击减少缩进按钮
   - 调整行间距数值，验证效果
   - 调整段落间距数值，验证效果

#### 3. 图片功能测试
1. **图片上传**
   - 点击"插入图片"按钮
   - 选择图片文件上传
   - 验证图片是否正确插入到文档中

2. **图片操作**
   - 点击图片，验证是否显示选中状态
   - 拖动图片，验证是否可以改变位置
   - 验证图片调整大小功能（如果实现了控制点）

#### 4. 页面设置测试
1. **打开页面设置**
   - 点击"页面设置"按钮
   - 验证对话框是否正常打开

2. **纸张设置**
   - 更改纸张大小（A4、A3、Letter）
   - 更改页面方向（纵向、横向）
   - 点击确定，验证页面容器是否相应调整

3. **边距设置**
   - 调整上、下、左、右边距数值
   - 点击确定，验证页面内容区域是否相应调整

4. **页眉页脚设置**
   - 启用页眉，输入页眉内容
   - 启用页脚，输入页脚内容
   - 启用页码显示
   - 调整页眉页脚字体样式
   - 点击确定，验证页眉页脚是否正确显示

#### 5. 预览功能测试
1. **文档预览**
   - 点击"预览"按钮
   - 验证是否打开新窗口显示预览内容
   - 验证预览内容是否与编辑器中的内容一致
   - 验证页眉页脚是否正确显示

### 后端功能测试

#### 1. 启动后端服务
1. 启动后端项目：`cd logictrue-word && mvn spring-boot:run`
2. 验证服务是否正常启动（默认端口8080）

#### 2. 导出功能测试
1. **基本导出测试**
   - 在前端编辑器中输入内容
   - 点击"导出"按钮
   - 验证是否成功下载docx文件
   - 用Microsoft Word或WPS打开文件，验证内容是否正确

2. **复杂内容导出测试**
   - 输入包含多种格式的文本（加粗、斜体、不同字体大小等）
   - 插入图片
   - 设置页眉页脚
   - 导出并验证所有格式是否正确保留

3. **页面设置导出测试**
   - 设置不同的纸张大小和方向
   - 设置不同的页边距
   - 导出并验证页面设置是否正确应用

#### 3. API接口测试
可以使用Postman或其他API测试工具测试后端接口：

1. **导出接口测试**
   ```
   POST http://localhost:8080/word/simpleWordExport/export
   Content-Type: application/json
   
   {
     "title": "测试文档",
     "content": "<p>这是测试内容</p>",
     "pageSettings": {
       "paperSize": "A4",
       "orientation": "portrait",
       "marginTop": 25,
       "marginBottom": 25,
       "marginLeft": 30,
       "marginRight": 30,
       "showHeader": true,
       "headerText": "测试页眉",
       "showFooter": true,
       "footerText": "测试页脚",
       "showPageNumber": true
     }
   }
   ```

2. **预览接口测试**
   ```
   POST http://localhost:8080/word/simpleWordExport/preview
   Content-Type: application/json
   
   {
     "title": "测试文档",
     "content": "<p>这是测试内容</p>",
     "pageSettings": {
       "showHeader": true,
       "headerText": "测试页眉"
     }
   }
   ```

3. **验证接口测试**
   ```
   POST http://localhost:8080/word/simpleWordExport/validate
   Content-Type: application/json
   
   {
     "title": "测试文档",
     "content": "<p>这是测试内容</p>"
   }
   ```

## 常见问题和解决方案

### 1. 前端问题
- **页面无法加载**：检查路由配置是否正确
- **API调用失败**：检查后端服务是否启动，网络请求是否正确
- **样式显示异常**：检查CSS样式是否正确加载

### 2. 后端问题
- **导出失败**：检查POI依赖是否正确，Jsoup依赖是否添加
- **文件下载失败**：检查响应头设置是否正确
- **HTML解析错误**：检查Jsoup解析逻辑是否正确

### 3. 导出文件问题
- **格式丢失**：检查HTML到Word的转换逻辑
- **图片显示异常**：检查Base64图片解码和插入逻辑
- **页面设置无效**：检查页面属性设置代码

## 性能优化建议

1. **前端优化**
   - 对大文档内容进行分页处理
   - 优化图片上传和显示性能
   - 添加加载状态提示

2. **后端优化**
   - 添加请求参数验证
   - 优化HTML解析性能
   - 添加文件大小限制

3. **用户体验优化**
   - 添加操作提示和帮助信息
   - 优化错误提示信息
   - 添加快捷键支持

## 测试完成标准

- [ ] 所有文本编辑功能正常工作
- [ ] 图片上传和操作功能正常
- [ ] 页面设置功能正常
- [ ] 导出功能正常，生成的Word文档格式正确
- [ ] 所有API接口响应正常
- [ ] 错误处理机制工作正常
- [ ] 用户界面友好，操作流畅

## 后续改进建议

1. 添加更多文本格式选项（字体背景色、上标下标等）
2. 支持表格插入和编辑
3. 支持更多图片格式和操作
4. 添加文档模板功能
5. 支持协作编辑功能
6. 添加撤销/重做功能
7. 支持导出为PDF格式
