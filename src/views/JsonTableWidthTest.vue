<template>
  <div class="width-test">
    <h1>JsonTableContainer 宽度测试</h1>
    
    <div class="test-controls">
      <button @click="loadTestData1" class="test-btn">测试数据1 (4列)</button>
      <button @click="loadTestData2" class="test-btn">测试数据2 (6列)</button>
      <button @click="loadTestData3" class="test-btn">测试数据3 (8列)</button>
      <button @click="clearData" class="test-btn">清空数据</button>
    </div>

    <div class="debug-info">
      <h3>调试信息：</h3>
      <p>打开浏览器控制台查看详细的宽度计算日志</p>
    </div>

    <div class="table-container">
      <JsonTableContainer
        ref="testTable"
        :table-width="'100%'"
        :table-height="'400px'"
        :use-dynamic-header="true"
        :enable-nested-tables="true"
      />
    </div>
  </div>
</template>

<script>
import JsonTableContainer from '@/components/JsonTableContainer.vue'

export default {
  name: 'JsonTableWidthTest',
  components: {
    JsonTableContainer
  },
  methods: {
    loadTestData1() {
      const testData = {
        "headerConfig": {
          "headers": [["项目", "内容", "状态", "备注"]],
          "merges": []
        },
        "headerWidthConfig": {
          "columnWidths": [200, 300, 100, 150],
          "headerHeights": [40],
          "verticalHeaders": [false, false, false, false]
        },
        "cellRows": [
          [
            {"content": "测试项目1", "originContent": "测试项目1"},
            {"content": "这是一个测试内容", "originContent": "这是一个测试内容"},
            {"content": "进行中", "originContent": "进行中"},
            {"content": "重要", "originContent": "重要"}
          ],
          [
            {"content": "测试项目2", "originContent": "测试项目2"},
            {"content": "另一个测试内容", "originContent": "另一个测试内容"},
            {"content": "已完成", "originContent": "已完成"},
            {"content": "一般", "originContent": "一般"}
          ]
        ]
      }

      console.log('加载测试数据1 (4列):', testData)
      this.$refs.testTable.insertDataFromJSON(testData, { clearExisting: true })
    },

    loadTestData2() {
      const testData = {
        "headerConfig": {
          "headers": [["项目", "内容", "状态", "优先级", "负责人", "截止日期"]],
          "merges": []
        },
        "headerWidthConfig": {
          "columnWidths": [150, 250, 80, 80, 100, 120],
          "headerHeights": [40],
          "verticalHeaders": [false, false, false, false, false, false]
        },
        "cellRows": [
          [
            {"content": "项目A", "originContent": "项目A"},
            {"content": "详细描述A", "originContent": "详细描述A"},
            {"content": "进行中", "originContent": "进行中"},
            {"content": "高", "originContent": "高"},
            {"content": "张三", "originContent": "张三"},
            {"content": "2024-01-31", "originContent": "2024-01-31"}
          ]
        ]
      }

      console.log('加载测试数据2 (6列):', testData)
      this.$refs.testTable.insertDataFromJSON(testData, { clearExisting: true })
    },

    loadTestData3() {
      const testData = {
        "headerConfig": {
          "headers": [
            ["检查项目", "技术要求", "检查结果", "完工", "", "检查员", "组长", "检验员"],
            ["", "", "", "月", "日", "", "", ""]
          ],
          "merges": [
            { "startRow": 0, "startCol": 0, "endRow": 1, "endCol": 0, "content": "检查项目" },
            { "startRow": 0, "startCol": 1, "endRow": 1, "endCol": 1, "content": "技术要求" },
            { "startRow": 0, "startCol": 2, "endRow": 1, "endCol": 2, "content": "检查结果" },
            { "startRow": 0, "startCol": 3, "endRow": 0, "endCol": 4, "content": "完工" },
            { "startRow": 0, "startCol": 5, "endRow": 1, "endCol": 5, "content": "检查员" },
            { "startRow": 0, "startCol": 6, "endRow": 1, "endCol": 6, "content": "组长" },
            { "startRow": 0, "startCol": 7, "endRow": 1, "endCol": 7, "content": "检验员" }
          ]
        },
        "headerWidthConfig": {
          "columnWidths": [150, 200, 150, 50, 50, 80, 80, 80],
          "headerHeights": [50, 50],
          "verticalHeaders": [false, false, false, false, false, true, true, true]
        },
        "cellRows": [
          [
            {"content": "基础检查", "originContent": "基础检查"},
            {"content": "符合标准要求", "originContent": "符合标准要求"},
            {"content": "合格", "originContent": "合格"},
            {"content": "12", "originContent": "12"},
            {"content": "15", "originContent": "15"},
            {"content": "张三", "originContent": "张三"},
            {"content": "李四", "originContent": "李四"},
            {"content": "王五", "originContent": "王五"}
          ]
        ]
      }

      console.log('加载测试数据3 (8列，复杂表头):', testData)
      this.$refs.testTable.insertDataFromJSON(testData, { clearExisting: true })
    },

    clearData() {
      this.$refs.testTable.clearAllData()
      console.log('数据已清空')
    }
  }
}
</script>

<style scoped>
.width-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

.test-controls {
  margin-bottom: 20px;
}

.test-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  margin-right: 10px;
  border-radius: 4px;
  cursor: pointer;
}

.test-btn:hover {
  background-color: #0056b3;
}

.debug-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.debug-info h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.debug-info p {
  margin: 0;
  color: #666;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: auto;
}
</style>
